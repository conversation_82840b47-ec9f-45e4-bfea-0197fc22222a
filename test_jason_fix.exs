#!/usr/bin/env elixir

# Simple test script to verify <PERSON> serialization works
Mix.install([
  {:jason, "~> 1.4"},
  {:ecto, "~> 3.10"}
])

defmodule TestSerializable do
  defprotocol Dumper do
    @fallback_to_any true
    def dump(value)
  end

  defimpl Dumper, for: Any do
    def dump(value), do: value
  end

  defmacro __using__(_opts) do
    quote location: :keep do
      defimpl Jason.Encoder, for: __MODULE__ do
        def encode(component, _opts) do
          attributes = TestSerializable.Dumper.dump(Map.from_struct(component))

          Jason.encode!(%{
            __struct__: component.__struct__ |> to_string() |> String.split(".") |> List.last(),
            attributes: attributes
          })
        end
      end
    end
  end
end

defmodule TestStruct do
  defstruct [:name, :value]
  use TestSerializable
end

defmodule TestRunner do
  def run do
    # Test the serialization
    test_struct = %TestStruct{name: :test, value: 42}

    try do
      json = <PERSON>.encode!(test_struct)
      IO.puts("✅ Serialization successful!")
      IO.puts("JSON: #{json}")

      decoded = Jason.decode!(json)
      IO.puts("✅ Deserialization successful!")
      IO.inspect(decoded, label: "Decoded")
    rescue
      e ->
        IO.puts("❌ Error: #{inspect(e)}")
        System.halt(1)
    end
  end
end

TestRunner.run()
