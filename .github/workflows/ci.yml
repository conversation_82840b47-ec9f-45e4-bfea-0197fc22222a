name: CI

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

permissions:
  contents: read

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest

    strategy:
      matrix:
        include:
          - elixir: "1.14"
            otp: "24.3"

          - elixir: "1.15"
            otp: "25.3"

          - elixir: "1.16"
            otp: "26.2"

          - elixir: "1.17"
            otp: "27.3"

          - elixir: "1.18"
            otp: "28.0"

    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    env:
      MIX_ENV: test
      POSTGRES_HOST: localhost

    steps:
      - uses: actions/checkout@v4
      - name: Set up Elixir
        uses: erlef/setup-beam@v1
        with:
          otp-version: ${{ matrix.otp }}
          elixir-version: ${{ matrix.elixir }}

      - name: Restore dependencies cache
        uses: actions/cache@v4
        with:
          path: deps
          key: ${{ runner.os }}-mix-${{ hashFiles('**/mix.lock') }}
          restore-keys: ${{ runner.os }}-mix-

      - name: Install dependencies
        run: mix deps.get

      - name: Check formatting
        run: mix format --check-formatted
        if: matrix.elixir == '1.18'

      - name: Setup database
        run: mix do ecto.create, ecto.migrate

      - name: Run tests
        run: mix test
